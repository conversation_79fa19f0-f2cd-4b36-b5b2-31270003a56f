import 'package:flutter/material.dart';
import 'package:dalti_customer/core/theme/app_colors.dart';
import 'package:dalti_customer/core/routing/app_router.dart';

class SearchResultCard extends StatelessWidget {
  final String name;
  final String specialty;
  final double rating;
  final int reviewsCount;
  final String nextAvailable;
  final String locationName;
  final String providerId;

  const SearchResultCard({
    super.key,
    required this.name,
    required this.specialty,
    required this.rating,
    required this.reviewsCount,
    required this.nextAvailable,
    required this.locationName,
    required this.providerId,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.04),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      padding: const EdgeInsets.all(16),
      child: LayoutBuilder(
        builder: (context, constraints) {
          final isNarrow = constraints.maxWidth < 360;
          if (isNarrow) {
            // Stack button below content on narrow widths to avoid overflow
            return Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _logoPlaceholder(),
                    const SizedBox(width: 12),
                    Expanded(child: _content(context)),
                  ],
                ),
                const SizedBox(height: 12),
                Row(
                  children: [
                    Expanded(child: _locationAndAvailabilityColumn()),
                    const SizedBox(width: 12),
                    _bookNowButton(context),
                  ],
                ),
              ],
            );
          }

          // Wide layout: button at trailing side with flexible width
          return Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _logoPlaceholder(),
              const SizedBox(width: 12),
              Expanded(child: _content(context)),
              const SizedBox(width: 12),
              Flexible(
                fit: FlexFit.loose,
                child: Stack(
                  children: [
                    Padding(
                      padding: const EdgeInsets.only(right: 108),
                      child: _locationAndAvailabilityColumn(),
                    ),
                    Align(
                      alignment: Alignment.topRight,
                      child: _bookNowButton(context),
                    ),
                  ],
                ),
              ),
            ],
          );
        },
      ),
    );
  }

  Widget _logoPlaceholder() {
    return Container(
      width: 64,
      height: 64,
      decoration: BoxDecoration(
        color: AppColors.primary.withValues(alpha: 0.06),
        borderRadius: BorderRadius.circular(12),
      ),
      child: const Icon(Icons.work_outline, color: AppColors.primary, size: 28),
    );
  }

  Widget _content(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          name,
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w700,
            color: AppColors.textPrimary,
          ),
        ),
        const SizedBox(height: 2),
        Text(specialty, style: const TextStyle(color: AppColors.textSecondary)),
        const SizedBox(height: 8),
        _ratingRow(),
        const SizedBox(height: 6),
      ],
    );
  }

  Widget _ratingRow() {
    final fullStars = rating.floor();
    final hasHalf = (rating - fullStars) >= 0.5;
    return Row(
      children: [
        for (int i = 0; i < 5; i++)
          Icon(
            i < fullStars
                ? Icons.star
                : (i == fullStars && hasHalf
                      ? Icons.star_half
                      : Icons.star_border),
            size: 18,
            color: const Color(0xFFFFB74D),
          ),
        const SizedBox(width: 6),
        Text(
          '($reviewsCount)',
          style: const TextStyle(color: AppColors.textSecondary),
        ),
      ],
    );
  }

  Widget _locationAndAvailabilityColumn() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            const Icon(
              Icons.place_outlined,
              size: 16,
              color: AppColors.textSecondary,
            ),
            const SizedBox(width: 4),
            Expanded(
              child: Text(
                locationName,
                style: const TextStyle(
                  color: AppColors.textSecondary,
                  fontSize: 12,
                ),
                overflow: TextOverflow.ellipsis,
                maxLines: 1,
              ),
            ),
          ],
        ),
        const SizedBox(height: 2),
        Row(
          children: [
            const Icon(
              Icons.schedule,
              size: 16,
              color: AppColors.textSecondary,
            ),
            const SizedBox(width: 4),
            const Text(
              'Next Available:',
              style: TextStyle(color: AppColors.textSecondary, fontSize: 12),
            ),
            const SizedBox(width: 4),
            Flexible(
              child: Text(
                nextAvailable,
                style: const TextStyle(
                  color: AppColors.primary,
                  fontWeight: FontWeight.w600,
                  fontSize: 12,
                ),
                overflow: TextOverflow.ellipsis,
                maxLines: 1,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _bookNowButton(BuildContext context) {
    return ElevatedButton(
      onPressed: () => AppNavigation.goToBookAppointment(context, providerId),
      style: ElevatedButton.styleFrom(
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
        minimumSize: const Size(80, 30),
        maximumSize: const Size(100, 50),
        padding: const EdgeInsets.symmetric(horizontal: 14, vertical: 5),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
      ),
      child: const Text(
        'Book Now',
        style: TextStyle(fontWeight: FontWeight.w700, fontSize: 15),
      ),
    );
  }
}
